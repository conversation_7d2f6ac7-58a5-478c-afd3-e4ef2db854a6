# 🚀 Multi-Agent Web Scraping System

[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com/)
[![<PERSON><PERSON><PERSON><PERSON>](https://img.shields.io/badge/Lang<PERSON>hain-Latest-orange.svg)](https://langchain.com/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

An advanced, AI-powered web scraping system featuring multiple specialized agents, intelligent orchestration, and both CLI and Web interfaces. Built with LangChain for AI capabilities and Pydantic for data validation.

## 🌟 Features

### 🤖 Multi-Agent Architecture
- **5 Specialized Agents**: Orchestrator, Web Scraping, Document Processing, Data Transformation, and Data Output
- **AI-Powered Coordination**: Lang<PERSON>hain integration for intelligent decision making
- **Scalable Design**: Easily extensible with new agent types

### 🖥️ Dual Interface System
- **Enhanced CLI**: Natural language processing, interactive modes, real-time progress
- **Web API**: RESTful endpoints, WebSocket support, comprehensive documentation
- **Real-time Monitoring**: Live agent status, job progress, system metrics

### 🛡️ Enterprise Features
- **Authentication & Authorization**: JWT-based security with role management
- **Rate Limiting**: Configurable limits to prevent abuse
- **Job Scheduling**: Priority-based queue with recurring tasks
- **Comprehensive Logging**: Detailed audit trails and error tracking

## 📋 Table of Contents

- [Installation](#installation)
- [Quick Start](#quick-start)
- [Architecture](#architecture)
- [Usage Methods](#usage-methods)
- [Command-Line Reference](#command-line-reference)
- [Examples by Complexity](#examples-by-complexity)
  - [Basic Examples](#basic-examples)
  - [Intermediate Examples](#intermediate-examples)
  - [Advanced Examples](#advanced-examples)
- [Configuration](#configuration)
- [API Documentation](#api-documentation)
- [Agent System](#agent-system)
- [Best Practices](#best-practices)
- [Troubleshooting](#troubleshooting)
- [Development](#development)
- [Contributing](#contributing)
- [License](#license)

## 🚀 Installation

### Prerequisites

- Python 3.8 or higher
- pip package manager
- Git

### Clone Repository

```bash
git clone https://github.com/MAg15TIq/web-scrapper.git
cd web-scrapper
```

### Install Dependencies

```bash
# Create virtual environment (recommended)
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Install additional system dependencies
python -m playwright install  # For JavaScript rendering
python -m spacy download en_core_web_sm  # For NLP features
```

### Environment Setup

```bash
# Copy environment template (if available)
cp .env.example .env

# Or create a new .env file with these variables:
# WEB_HOST=0.0.0.0
# WEB_PORT=8000
# WEB_DEBUG=false
# SECRET_KEY=your-super-secret-key-here
# OPENAI_API_KEY=your-openai-api-key  # Optional for AI features
# DATABASE_URL=sqlite:///./webscraper.db
# REDIS_URL=redis://localhost:6379/0  # Optional for advanced features
```

### System Requirements

**For JavaScript Rendering:**
- Playwright browsers will be installed automatically
- Requires ~200MB disk space for browser binaries

**For OCR Features (Optional):**
- **Ubuntu/Debian:** `sudo apt-get install tesseract-ocr`
- **macOS:** `brew install tesseract`
- **Windows:** Download from [Tesseract GitHub](https://github.com/UB-Mannheim/tesseract/wiki)

## ⚡ Quick Start

### 1. Install Dependencies

```bash
# Install Python dependencies
pip install -r requirements.txt

# Install Playwright browsers (required for JavaScript-heavy sites)
python -m playwright install

# Create output directory
mkdir output
```

### 2. Basic Usage - Command Line

```bash
# Simple scraping with URL
python main.py scrape --url https://quotes.toscrape.com/

# Interactive mode
python main.py scrape --interactive

# List all available agents
python main.py agents

# Test basic functionality
python simple_test.py
```

### 3. Verify Installation

```bash
# Run the simple test to verify everything works
python simple_test.py

# Expected output: Successfully extracts quotes and saves to output/quotes.json
```

## 🏗️ Architecture

### System Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Enhanced CLI  │    │   Web Frontend  │    │   Mobile App    │
│  (Natural Lang) │    │   (React/Vue)   │    │   (Future)      │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │      FastAPI Server       │
                    │   • Authentication        │
                    │   • Rate Limiting         │
                    │   • WebSocket Support     │
                    │   • Job Management        │
                    └─────────────┬─────────────┘
                                  │
                    ┌─────────────┴─────────────┐
                    │   LangChain Framework     │
                    │   • AI Orchestration      │
                    │   • Natural Language      │
                    │   • Decision Making       │
                    └─────────────┬─────────────┘
                                  │
        ┌─────────────────────────┼─────────────────────────┐
        │                         │                         │
┌───────┴───────┐    ┌───────────┴────────────┐    ┌───────┴───────┐
│ Orchestrator  │    │    Specialized         │    │  Data Output  │
│    Agent      │    │      Agents            │    │    Agent      │
│               │    │ • Web Scraping         │    │               │
│ • Coordinates │    │ • Document Processing  │    │ • JSON Export │
│ • Plans       │    │ • Data Transformation  │    │ • CSV Export  │
│ • Monitors    │    │ • Error Recovery       │    │ • Database    │
└───────────────┘    └────────────────────────┘    └───────────────┘
```

### Agent Types

1. **Orchestrator Agent** 🎯
   - Coordinates all scraping operations
   - Plans execution strategies
   - Monitors system health

2. **Web Scraping Agent** 🕷️
   - Handles HTTP requests
   - Manages sessions and cookies
   - Implements anti-detection measures

3. **Document Processing Agent** 📄
   - Processes PDFs and documents
   - Extracts text and metadata
   - Handles various file formats

4. **Data Transformation Agent** 🔄
   - Cleans and normalizes data
   - Applies transformation rules
   - Validates data quality

5. **Data Output Agent** 💾
   - Manages data export
   - Supports multiple formats
   - Handles database operations

## 📖 Usage Methods

### 🎯 Five Ways to Use the Web Scraper

The Multi-Agent Web Scraping System offers **5 different approaches** to scrape websites, each suited for different use cases:

1. **🖥️ Command Line Interface (CLI)** - Direct commands for quick scraping
2. **🤖 Interactive Mode** - AI-powered conversational interface with guided setup
3. **🌐 Web API** - RESTful API for integration with other applications
4. **📝 Python Scripts** - Custom scripts using the agent framework directly
5. **⚡ Simple Test Mode** - Quick testing and validation without complex setup

---

### 1. 🖥️ Command Line Interface (CLI)

#### Basic Commands
```bash
# Scrape a single URL
python main.py scrape --url https://example.com

# Scrape with custom output file
python main.py scrape --url https://quotes.toscrape.com --output quotes_data.json

# List all available agents and their capabilities
python main.py agents

# Get help for any command
python main.py --help
python main.py scrape --help
```

#### Advanced CLI Options
```bash
# Scrape multiple pages with pagination
python main.py scrape --url https://example.com --max-pages 5

# Use specific agents for specialized tasks
python main.py scrape --url https://example.com --agents scraper,parser,storage

# Set custom delays and rate limiting
python main.py scrape --url https://example.com --delay 2 --rate-limit 10

# Enable verbose logging
python main.py scrape --url https://example.com --verbose
```

### 2. 🤖 Interactive Mode

```bash
# Start interactive session with AI assistance
python main.py scrape --interactive

# The system will guide you through:
# 1. URL input and validation
# 2. Automatic agent selection
# 3. Real-time progress monitoring
# 4. Results preview and export options
```

### 3. 🌐 Web API Mode

```bash
# Start the web server (if available)
python web/api/main.py

# API will be available at:
# - Main API: http://localhost:8000
# - Documentation: http://localhost:8000/api/docs
```

### 4. 📝 Python Script Integration

```python
# Create custom scraping scripts
from agents.scraper import ScraperAgent
from agents.parser import ParserAgent

# Initialize agents
scraper = ScraperAgent()
parser = ParserAgent()

# Custom scraping logic
data = scraper.scrape("https://example.com")
parsed_data = parser.parse(data)
```

### 5. ⚡ Simple Test Mode

```bash
# Quick test with built-in examples
python simple_test.py

# Test specific functionality
python examples/simple_scrape.py
python examples/advanced_scrape.py
python examples/javascript_example.py
```

---

## 🎯 Command-Line Reference

### Basic Commands

```bash
# Display help and available commands
python main.py --help
python main.py scrape --help

# List all available agents and their capabilities
python main.py agents

# Check system status and health
python main.py status  # (if available)
```

### Scraping Commands

```bash
# Basic scraping
python main.py scrape --url "https://example.com"

# Scrape with custom output
python main.py scrape --url "https://example.com" --output "my_data.json"

# Specify output format
python main.py scrape --url "https://example.com" --format csv
python main.py scrape --url "https://example.com" --format excel
python main.py scrape --url "https://example.com" --format sqlite
```

### Advanced Options

```bash
# Multi-page scraping with pagination
python main.py scrape --url "https://example.com" --max-pages 5

# JavaScript rendering for dynamic content
python main.py scrape --url "https://spa-example.com" --render-js

# Anti-detection measures
python main.py scrape --url "https://example.com" --anti-detection

# Custom CSS selectors for data extraction
python main.py scrape --url "https://example.com" --selectors "title:h1,price:.price"

# Rate limiting and delays
python main.py scrape --url "https://example.com" --delay 2 --rate-limit 10

# Use specific agents
python main.py scrape --url "https://example.com" --agents "scraper,parser,storage"

# Clean and normalize data
python main.py scrape --url "https://example.com" --clean-data

# Verbose logging for debugging
python main.py scrape --url "https://example.com" --verbose
```

### Configuration File Usage

```bash
# Use a configuration file
python main.py scrape --config examples/config.yaml

# Use a specific configuration profile
python main.py scrape --config config.yaml --profile production
```

---

## 📚 Examples by Complexity

### 🟢 **BASIC Examples** - Getting Started

Perfect for beginners who want to understand the basic functionality.

#### Example 1: Simple Quote Scraping
```bash
# Scrape quotes from a test website
python main.py scrape --url https://quotes.toscrape.com/

# Expected Output:
# ✅ 10 quotes extracted
# ✅ Authors and tags included
# ✅ Saved to output/quotes.json
```

**What this does:**
- Fetches the webpage content
- Automatically detects quote elements
- Extracts text, author, and tags
- Saves data in JSON format

**Expected Result:**
```json
[
  {
    "text": "The world as we have created it is a process of our thinking...",
    "author": "Albert Einstein",
    "tags": ["change", "deep-thoughts", "thinking", "world"]
  }
]
```

#### Example 2: News Headlines
```bash
# Scrape news headlines
python main.py scrape --url https://news.ycombinator.com/

# What it extracts:
# - Article titles
# - Links
# - Scores/votes
# - Comments count
```

#### Example 3: Product Information
```bash
# Basic product scraping
python main.py scrape --url https://books.toscrape.com/

# Extracts:
# - Book titles
# - Prices
# - Availability status
# - Star ratings
```

#### Example 4: Using the Simple Test Script
```bash
# Run the built-in simple test
python simple_test.py

# This demonstrates:
# - Basic HTTP requests
# - HTML parsing with BeautifulSoup
# - Data extraction and JSON export
# - Error handling
```

---

### 🟡 **INTERMEDIATE Examples** - Advanced Features

For users who need more sophisticated scraping capabilities.

#### Example 1: Multi-Page E-commerce Scraping
```bash
# Scrape multiple pages with pagination
python main.py scrape \
  --url https://books.toscrape.com/ \
  --max-pages 5 \
  --delay 2 \
  --output books_catalog.json

# Features used:
# 🔄 Automatic pagination detection
# ⏱️ Rate limiting (2-second delays)
# 📊 Progress tracking
# 💾 Custom output file
```

**What this does:**
- Automatically detects "Next" buttons or pagination links
- Scrapes up to 5 pages of book listings
- Waits 2 seconds between requests (respectful scraping)
- Saves all data to a single JSON file

#### Example 2: JavaScript-Heavy Site Scraping
```bash
# Scrape sites that require JavaScript execution
python main.py scrape \
  --url https://quotes.toscrape.com/js/ \
  --render-js \
  --output js_quotes.json

# This handles:
# ⚡ JavaScript rendering with Playwright
# 🔄 Dynamic content loading
# ⏳ Waiting for elements to appear
# 📸 Optional screenshot capture
```

#### Example 3: Custom Data Extraction with Selectors
```bash
# Extract specific data using CSS selectors
python main.py scrape \
  --url https://books.toscrape.com/ \
  --selectors "title:h3 a,price:.price_color,rating:.star-rating" \
  --format csv \
  --output books.csv
```

#### Example 4: Using the Advanced Python Script
```bash
# Run the advanced example script
python examples/advanced_scrape.py

# This demonstrates:
# - Anti-detection fingerprinting
# - Request pattern optimization
# - Data cleaning and transformation
# - Text analysis and sentiment detection
```

#### Example 5: JavaScript Rendering Example
```bash
# Run the JavaScript example
python examples/javascript_example.py

# Features demonstrated:
# - Page rendering with Playwright
# - Waiting for dynamic content
# - Page scrolling and interaction
# - Screenshot capture
# - Custom JavaScript execution
```

---

### 🔴 **ADVANCED Examples** - Complex Scenarios

For power users who need enterprise-level scraping capabilities.

#### Example 1: E-commerce Site with Authentication
```python
# File: advanced_ecommerce_scrape.py
import asyncio
from agents.coordinator import CoordinatorAgent
from agents.authentication import AuthenticationAgent
from agents.anti_detection import AntiDetectionAgent

async def scrape_with_login():
    coordinator = CoordinatorAgent()
    auth_agent = AuthenticationAgent(coordinator_id=coordinator.agent_id)
    anti_detection = AntiDetectionAgent(coordinator_id=coordinator.agent_id)

    # Register agents
    coordinator.register_agent(auth_agent)
    coordinator.register_agent(anti_detection)

    # Login configuration
    login_config = {
        "url": "https://example-shop.com/login",
        "credentials": {
            "username": "your_username",
            "password": "your_password"
        },
        "selectors": {
            "username_field": "#username",
            "password_field": "#password",
            "submit_button": "#login-btn"
        }
    }

    # Scraping configuration
    scrape_config = {
        "url": "https://example-shop.com/products",
        "selectors": {
            "product_name": ".product-title",
            "price": ".price",
            "description": ".product-desc",
            "availability": ".stock-status"
        },
        "pagination": {
            "max_pages": 20,
            "next_selector": ".pagination .next"
        },
        "anti_detection": {
            "rotate_user_agents": True,
            "random_delays": True,
            "proxy_rotation": False
        }
    }

    # Execute scraping with authentication
    result = await coordinator.execute_authenticated_scraping(
        login_config, scrape_config
    )
    return result

# Run: python advanced_ecommerce_scrape.py
```

#### Example 2: Multi-Site Data Aggregation
```python
# File: multi_site_aggregator.py
import asyncio
from agents.coordinator import CoordinatorAgent
from agents.data_transformation import DataTransformationAgent

async def aggregate_job_data():
    coordinator = CoordinatorAgent()

    sites_config = [
        {
            "name": "Site1",
            "url": "https://jobs-site1.com/search",
            "selectors": {
                "title": ".job-title",
                "company": ".company",
                "location": ".location",
                "salary": ".salary"
            }
        },
        {
            "name": "Site2",
            "url": "https://jobs-site2.com/listings",
            "selectors": {
                "title": "h2.title",
                "company": ".employer",
                "location": ".city",
                "salary": ".compensation"
            }
        }
    ]

    # Scrape all sites
    all_data = []
    for site in sites_config:
        data = await coordinator.scrape_site(site)
        all_data.extend(data)

    # Normalize and deduplicate data
    transformation_config = {
        "operations": [
            {"field": "salary", "operation": "normalize_currency"},
            {"field": "location", "operation": "standardize_location"},
            {"operation": "remove_duplicates", "key_fields": ["title", "company"]}
        ]
    }

    cleaned_data = await coordinator.transform_data(all_data, transformation_config)
    return cleaned_data

# Run: python multi_site_aggregator.py
```

#### Example 3: Real-time Monitoring and Alerts
```python
# File: price_monitor.py
import asyncio
import schedule
import time
from agents.coordinator import CoordinatorAgent
from agents.monitoring import MonitoringAgent

class PriceMonitor:
    def __init__(self):
        self.coordinator = CoordinatorAgent()
        self.monitoring_agent = MonitoringAgent(coordinator_id=self.coordinator.agent_id)

    async def monitor_prices(self):
        products = [
            {
                "name": "iPhone 15",
                "url": "https://store.example.com/iphone-15",
                "price_selector": ".price",
                "target_price": 800
            },
            {
                "name": "MacBook Pro",
                "url": "https://store.example.com/macbook-pro",
                "price_selector": ".current-price",
                "target_price": 1500
            }
        ]

        for product in products:
            current_price = await self.scrape_price(product["url"], product["price_selector"])

            if current_price <= product["target_price"]:
                await self.send_alert(product["name"], current_price, product["target_price"])

    async def scrape_price(self, url, selector):
        # Implementation for price scraping
        pass

    async def send_alert(self, product_name, current_price, target_price):
        # Implementation for sending alerts (email, SMS, etc.)
        pass

# Schedule monitoring every hour
monitor = PriceMonitor()
schedule.every().hour.do(lambda: asyncio.run(monitor.monitor_prices()))

while True:
    schedule.run_pending()
    time.sleep(60)
```

---

## ⚙️ Configuration

### Environment Variables

```bash
# Web Server
WEB_HOST=0.0.0.0
WEB_PORT=8000
WEB_DEBUG=false

# Security
SECRET_KEY=your-super-secret-key-here
OPENAI_API_KEY=your-openai-api-key

# Database
DATABASE_URL=sqlite:///./webscraper.db
REDIS_URL=redis://localhost:6379/0

# Features
WEB_ENABLE_AUTH=true
WEB_ENABLE_RATE_LIMITING=true
WEB_ENABLE_WEBSOCKETS=true
```

### Configuration Files

#### Main Configuration (config/defaults.yaml)
```yaml
# Scraper settings
scraper:
  user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
  timeout: 30
  max_retries: 3
  retry_delay: 1.0
  follow_redirects: true
  verify_ssl: true

# Rate limiting
rate_limiting:
  enabled: true
  default_rate: 1  # Requests per period
  default_period: 2.0  # Period in seconds
  adaptive: true  # Adjust rate based on server responses

# Parser settings
parser:
  default_parser: "html.parser"  # Options: "html.parser", "lxml", "html5lib"
  normalize_whitespace: true
  extract_metadata: true

# Storage settings
storage:
  output_dir: "output"
  default_format: "json"  # Options: "json", "csv", "excel", "sqlite"
  pretty_json: true
  csv_delimiter: ","
  excel_engine: "openpyxl"

# Proxy settings (optional)
proxy:
  enabled: false
  rotation_enabled: true
  proxy_list_path: null  # Path to a file containing proxies
  check_interval: 600  # Seconds between proxy health checks
```

#### CLI Configuration
```yaml
# ~/.webscraper_cli/config.yaml
version: "1.0.0"
default_profile: "default"

profiles:
  default:
    name: "default"
    default_output_format: "json"
    auto_confirm: false
    theme: "default"

  production:
    name: "production"
    default_output_format: "csv"
    auto_confirm: true
    theme: "minimal"
    rate_limit: 0.5  # More conservative for production

logging:
  level: "INFO"
  file: "logs/cli.log"
  max_file_size: "10MB"
  backup_count: 5
```

#### Example Project Configuration
```yaml
# examples/config.yaml
project:
  name: "E-commerce Product Scraper"
  description: "Scrape product data from multiple e-commerce sites"

targets:
  - name: "books"
    url: "https://books.toscrape.com/"
    selectors:
      title: "h3 a"
      price: ".price_color"
      rating: ".star-rating"
    pagination:
      enabled: true
      max_pages: 5
      next_selector: ".next a"

  - name: "quotes"
    url: "https://quotes.toscrape.com/"
    selectors:
      text: ".quote .text"
      author: ".quote .author"
      tags: ".quote .tags .tag"

output:
  format: "json"
  path: "output/{name}_data.json"

settings:
  delay: 2.0
  user_agent: "Custom Scraper 1.0"
  respect_robots_txt: true
```

---

## 🌐 API Documentation

### Starting the Web API

```bash
# Start the development server
python web/api/main.py

# Or using uvicorn directly
uvicorn web.api.main:app --host 0.0.0.0 --port 8000 --reload

# API will be available at:
# - Main API: http://localhost:8000
# - Interactive docs: http://localhost:8000/docs
# - ReDoc docs: http://localhost:8000/redoc
```

### API Endpoints

#### Authentication
```bash
# Register a new user
curl -X POST "http://localhost:8000/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{"username": "user", "email": "<EMAIL>", "password": "password"}'

# Login
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "user", "password": "password"}'
```

#### Job Management
```bash
# Create a scraping job
curl -X POST "http://localhost:8000/api/v1/jobs" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://quotes.toscrape.com/",
    "selectors": {
      "quote": ".quote .text",
      "author": ".quote .author"
    },
    "output_format": "json"
  }'

# Get job status
curl -X GET "http://localhost:8000/api/v1/jobs/{job_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"

# List all jobs
curl -X GET "http://localhost:8000/api/v1/jobs" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### Agent Management
```bash
# List available agents
curl -X GET "http://localhost:8000/api/v1/agents"

# Get agent details
curl -X GET "http://localhost:8000/api/v1/agents/{agent_id}"

# Update agent configuration
curl -X PUT "http://localhost:8000/api/v1/agents/{agent_id}" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"config": {"timeout": 30, "retries": 3}}'
```

### WebSocket Real-time Updates

```javascript
// Connect to WebSocket for real-time job updates
const ws = new WebSocket('ws://localhost:8000/ws/jobs');

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('Job update:', data);
    // Handle job status updates, progress, etc.
};

// Send commands via WebSocket
ws.send(JSON.stringify({
    "action": "start_job",
    "job_id": "12345"
}));
```

---

## 🤖 Agent System

### Available Agents

#### 1. **Coordinator Agent** 🎯
- **Purpose**: Orchestrates all scraping operations
- **Capabilities**: Task planning, agent coordination, resource management
- **Use Cases**: Complex multi-step scraping workflows

#### 2. **Scraper Agent** 🕷️
- **Purpose**: Handles HTTP requests and content fetching
- **Capabilities**: Session management, cookie handling, request optimization
- **Use Cases**: Basic web scraping, API interactions

#### 3. **JavaScript Agent** ⚡
- **Purpose**: Renders JavaScript-heavy pages
- **Capabilities**: Browser automation, dynamic content loading, screenshot capture
- **Use Cases**: SPAs, dynamic content, interactive pages

#### 4. **Parser Agent** 🔍
- **Purpose**: Extracts structured data from HTML/XML
- **Capabilities**: CSS selectors, XPath, regex patterns, data normalization
- **Use Cases**: Data extraction, content parsing, metadata extraction

#### 5. **Storage Agent** 💾
- **Purpose**: Manages data export and storage
- **Capabilities**: Multiple formats (JSON, CSV, Excel, SQLite), database integration
- **Use Cases**: Data persistence, format conversion, database operations

#### 6. **Authentication Agent** 🔐
- **Purpose**: Handles login and session management
- **Capabilities**: Form-based login, OAuth, session persistence, multi-factor auth
- **Use Cases**: Protected content, user-specific data, authenticated APIs

#### 7. **Anti-Detection Agent** 🛡️
- **Purpose**: Implements stealth and anti-detection measures
- **Capabilities**: User agent rotation, proxy management, fingerprint randomization
- **Use Cases**: Bot detection avoidance, large-scale scraping, protected sites

#### 8. **Data Transformation Agent** 🔄
- **Purpose**: Cleans and transforms extracted data
- **Capabilities**: Data cleaning, normalization, validation, enrichment
- **Use Cases**: Data quality assurance, format standardization, data enrichment

#### 9. **Error Recovery Agent** 🔧
- **Purpose**: Handles errors and implements recovery strategies
- **Capabilities**: Retry logic, fallback strategies, error analysis
- **Use Cases**: Robust scraping, error handling, system resilience

#### 10. **Monitoring Agent** 📊
- **Purpose**: Tracks system performance and health
- **Capabilities**: Metrics collection, alerting, performance optimization
- **Use Cases**: System monitoring, performance tuning, operational insights

---

## 🧪 Testing

### Run Tests
```bash
# Install test dependencies
pip install pytest pytest-cov pytest-asyncio

# Run all tests
pytest

# Run with coverage
pytest --cov=. --cov-report=html

# Run specific test categories
pytest tests/test_cli/
pytest tests/test_web_api/
pytest tests/test_integration/
```

### Manual Testing
```bash
# Test CLI functionality
python main.py --help
python main.py agents

# Test basic scraping
python simple_test.py

# Test advanced features
python examples/simple_scrape.py
python examples/advanced_scrape.py
python examples/javascript_example.py

# Test API health (if web server is running)
curl http://localhost:8000/health

# Test WebSocket connection
# Use a WebSocket client to connect to ws://localhost:8000/ws/test
```

---

## 📋 Best Practices

### Responsible Web Scraping

#### 1. **Respect robots.txt**
```bash
# Check robots.txt before scraping
curl https://example.com/robots.txt

# The scraper automatically checks robots.txt when configured
python main.py scrape --url "https://example.com" --respect-robots-txt
```

#### 2. **Implement Rate Limiting**
```bash
# Use appropriate delays between requests
python main.py scrape --url "https://example.com" --delay 2

# Use adaptive rate limiting
python main.py scrape --url "https://example.com" --rate-limit 10
```

#### 3. **Use Appropriate User Agents**
```python
# Configure realistic user agents
config = {
    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "rotate_user_agents": True
}
```

#### 4. **Handle Errors Gracefully**
```python
# Implement proper error handling
try:
    result = await coordinator.scrape_url(url)
except ScrapingError as e:
    logger.error(f"Scraping failed: {e}")
    # Implement fallback strategy
```

### Performance Optimization

#### 1. **Use Concurrent Scraping**
```python
# Scrape multiple URLs concurrently
urls = ["url1", "url2", "url3"]
tasks = [coordinator.scrape_url(url) for url in urls]
results = await asyncio.gather(*tasks)
```

#### 2. **Optimize Selectors**
```python
# Use efficient CSS selectors
selectors = {
    "title": "h1",  # Fast
    "price": ".price",  # Fast
    "description": "div.content p"  # More specific, still efficient
}
```

#### 3. **Cache Responses**
```python
# Enable response caching for repeated requests
config = {
    "cache_enabled": True,
    "cache_ttl": 3600  # 1 hour
}
```

### Data Quality

#### 1. **Validate Extracted Data**
```python
# Use data validation
from pydantic import BaseModel

class ProductData(BaseModel):
    title: str
    price: float
    availability: bool

# Validate extracted data
validated_data = ProductData(**extracted_data)
```

#### 2. **Clean and Normalize Data**
```bash
# Use data cleaning features
python main.py scrape --url "https://example.com" --clean-data
```

#### 3. **Handle Missing Data**
```python
# Implement fallback values
selectors = {
    "title": "h1, .title, .product-name",  # Multiple fallback selectors
    "price": ".price, .cost, .amount"
}
```

---

## 🔧 Troubleshooting

### Common Issues and Solutions

#### 1. **Installation Issues**

**Problem**: `pip install` fails with dependency conflicts
```bash
# Solution: Use a fresh virtual environment
python -m venv fresh_env
fresh_env\Scripts\activate  # Windows
pip install --upgrade pip
pip install -r requirements.txt
```

**Problem**: Playwright browsers not installing
```bash
# Solution: Install browsers manually
python -m playwright install
python -m playwright install-deps  # Linux only
```

#### 2. **Scraping Issues**

**Problem**: "403 Forbidden" or "Access Denied" errors
```bash
# Solution: Use anti-detection measures
python main.py scrape --url "https://example.com" --anti-detection

# Or configure custom user agent
python main.py scrape --url "https://example.com" --user-agent "Custom Bot 1.0"
```

**Problem**: JavaScript content not loading
```bash
# Solution: Enable JavaScript rendering
python main.py scrape --url "https://spa-site.com" --render-js

# Or increase wait time
python main.py scrape --url "https://spa-site.com" --render-js --wait-for-js 5
```

**Problem**: Rate limiting or IP blocking
```bash
# Solution: Implement delays and rotation
python main.py scrape --url "https://example.com" --delay 5 --anti-detection
```

#### 3. **Data Issues**

**Problem**: Empty or incomplete data extraction
```bash
# Solution: Use verbose logging to debug
python main.py scrape --url "https://example.com" --verbose

# Check and adjust CSS selectors
python main.py scrape --url "https://example.com" --selectors "title:.custom-title"
```

**Problem**: Encoding issues with special characters
```python
# Solution: Specify encoding in configuration
config = {
    "encoding": "utf-8",
    "normalize_unicode": True
}
```

#### 4. **Performance Issues**

**Problem**: Slow scraping performance
```bash
# Solution: Optimize settings
python main.py scrape --url "https://example.com" --timeout 10 --max-retries 2

# Use concurrent processing for multiple URLs
python examples/advanced_scrape.py  # Includes optimization examples
```

### Debug Mode

```bash
# Enable debug logging
export PYTHONPATH=.
python main.py scrape --url "https://example.com" --verbose --debug

# Check log files
tail -f logs/scraper.log
```

### Getting Help

1. **Check the documentation**: Review this README and example scripts
2. **Enable verbose logging**: Use `--verbose` flag for detailed output
3. **Test with simple sites**: Start with `https://quotes.toscrape.com/`
4. **Check configuration**: Verify your config files and environment variables
5. **Update dependencies**: Ensure you have the latest versions installed

## 🛠️ Development

### Project Structure
```
web-scrapper/
├── agents/                 # Agent implementations
├── cli/                   # Enhanced CLI components
├── web/                   # Web API and dashboard
├── config/                # Configuration files
├── models/                # Data models
├── monitoring/            # Monitoring components
├── tests/                 # Test suites
├── logs/                  # Log files
├── requirements.txt       # Python dependencies
└── README.md             # This file
```

### Adding New Features

1. **New Agent Type**
   - Create agent class in `agents/`
   - Register with coordinator
   - Add configuration options

2. **New API Endpoint**
   - Add route in `web/api/routes/`
   - Update dependencies if needed
   - Add tests

3. **CLI Enhancement**
   - Extend command parser
   - Add new commands
   - Update help documentation

### Code Style
```bash
# Format code
black .

# Sort imports
isort .

# Lint code
flake8 .

# Type checking
mypy .
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow PEP 8 style guidelines
- Add tests for new functionality
- Update documentation
- Ensure all tests pass

---

## 📚 Additional Resources

### Learning Materials

- **Beginner Tutorial**: Start with `python simple_test.py`
- **Example Scripts**: Explore the `examples/` directory
- **Configuration Guide**: Check `config/defaults.yaml` for all options
- **API Documentation**: Visit `http://localhost:8000/docs` when running the web server

### External Documentation

- **BeautifulSoup**: [Beautiful Soup Documentation](https://www.crummy.com/software/BeautifulSoup/bs4/doc/)
- **Playwright**: [Playwright Python Documentation](https://playwright.dev/python/)
- **FastAPI**: [FastAPI Documentation](https://fastapi.tiangolo.com/)
- **LangChain**: [LangChain Documentation](https://python.langchain.com/)

### Community and Support

- **GitHub Discussions**: Share ideas and ask questions
- **Example Gallery**: Community-contributed examples
- **Best Practices Guide**: Learn from experienced users

---

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [LangChain](https://langchain.com/) for AI orchestration and intelligent agent coordination
- [FastAPI](https://fastapi.tiangolo.com/) for the high-performance web framework
- [Rich](https://rich.readthedocs.io/) for beautiful and interactive CLI interfaces
- [Pydantic](https://pydantic-docs.helpmanual.io/) for robust data validation and settings management
- [Playwright](https://playwright.dev/) for reliable browser automation and JavaScript rendering
- [BeautifulSoup](https://www.crummy.com/software/BeautifulSoup/) for HTML parsing and data extraction

## 📞 Support

- � **Issues**: [GitHub Issues](https://github.com/MAg15TIq/web-scrapper/issues)
- � **Discussions**: [GitHub Discussions](https://github.com/MAg15TIq/web-scrapper/discussions)
- 📖 **Documentation**: [Project Wiki](https://github.com/MAg15TIq/web-scrapper/wiki)
- 📧 **Email**: For enterprise support and custom solutions

### Quick Support Checklist

Before reporting issues:
1. ✅ Check this README for solutions
2. ✅ Try the troubleshooting section
3. ✅ Test with `python simple_test.py`
4. ✅ Enable verbose logging with `--verbose`
5. ✅ Check existing GitHub issues

---

**🚀 Happy Scraping!**

*Built with ❤️ for the web scraping community*
